import {
	DndContext,
	closestCenter,
	useDraggable,
	useDroppable,
} from "@dnd-kit/core";
import React from "react";
import SessionModal from "../SessionModal";
import useSessionScheduleGrid, {
	type SessionScheduleGridProps,
} from "./use-session-schedule-grid";

// Draggable session cell component
function DraggableSessionCell({
	id,
	children,
	hasSession,
	seeAll,
}: {
	id: string;
	children: React.ReactNode;
	hasSession: boolean;
	seeAll: boolean;
}) {
	const { attributes, listeners, setNodeRef, transform, isDragging } =
		useDraggable({
			id,
			disabled: !hasSession || seeAll, // Only allow dragging if there's a session and seeAll is not active
		});

	const style = transform
		? {
				transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
				opacity: isDragging ? 0.5 : 1,
			}
		: undefined;

	// For sessions, we need to handle both drag and click
	// We'll pass the drag handlers to the children and let them handle clicks
	return (
		<div ref={setNodeRef} style={style}>
			{React.cloneElement(children as React.ReactElement, {
				...(hasSession ? { ...listeners, ...attributes } : {}),
			})}
		</div>
	);
}

// Droppable session cell component
function DroppableSessionCell({
	id,
	children,
}: {
	id: string;
	children: React.ReactNode;
}) {
	const { isOver, setNodeRef } = useDroppable({
		id,
	});

	const style = {
		backgroundColor: isOver ? "rgba(255, 92, 155, 0.2)" : undefined,
	};

	return (
		<div ref={setNodeRef} style={style}>
			{children}
		</div>
	);
}

export default function SessionScheduleGrid({
	schedule,
	turn,
}: SessionScheduleGridProps) {
	const {
		days,
		seeAll,
		timeSlots,
		showNames,
		sessionModal,
		selectedWorker,
		selectedClient,
		getSessionForSlot,
		getSessionsForSlot,
		minutesToTimeString,
		handleCellClick,
		handleModalClose,
		hasAvailableSlotsForDay,
		hasBusySessionsForDay,
		handleMakeDayBusy,
		handleFreeDayBusy,
		handleDragEnd,
		shouldShowNamesForSession,
	} = useSessionScheduleGrid({
		schedule,
		turn,
	});

	return (
		<>
			<DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
				<div
					className="grid gap-1 text-xs"
					style={{
						gridTemplateColumns: `120px repeat(${days.length}, 1fr)`,
						gridTemplateRows: `auto repeat(${timeSlots.length}, minmax(64px, auto))`,
					}}
				>
					{/* Header row */}
					<div className="flex items-center justify-center rounded bg-neutral p-2 font-medium text-white">
						Hora
					</div>
					{days.map((day, dayIndex) => (
						<div
							key={day}
							className="rounded bg-base-300 p-2 text-center font-medium"
						>
							<div className="mb-1">{day}</div>
							{/* Day-level controls - only show when worker is selected and see all is not active */}
							{selectedWorker && !seeAll && (
								<div className="flex justify-center gap-1">
									{hasAvailableSlotsForDay(dayIndex) && (
										<button
											type="button"
											className="btn btn-xs btn-warning"
											onClick={() => handleMakeDayBusy(dayIndex)}
											title="Marcar día como ocupado"
										>
											Ocupar
										</button>
									)}
									{hasBusySessionsForDay(dayIndex) && (
										<button
											type="button"
											className="btn btn-xs bg-[#0064e0] text-white"
											onClick={() => handleFreeDayBusy(dayIndex)}
											title="Liberar día"
										>
											Liberar
										</button>
									)}
								</div>
							)}
						</div>
					))}

					{/* Time slots and content */}
					{timeSlots.map((slot, timeIndex) => (
						<React.Fragment key={`${slot.start}-${timeIndex}`}>
							<div className="flex items-center justify-center rounded bg-base-200 p-2 font-mono text-xs">
								{minutesToTimeString(slot.start)} -{" "}
								{minutesToTimeString(slot.end)}
							</div>
							{days.map((day, dayIndex) => {
								const existingSession = getSessionForSlot(dayIndex, timeIndex);
								const allSessionsInSlot = getSessionsForSlot(
									dayIndex,
									timeIndex,
								);
								// Filter out busy sessions (client.id === "0") for display purposes
								const nonBusySessionsInSlot = allSessionsInSlot.filter(
									(session) => session.client.id !== "0",
								);
								const hasMultipleSessions =
									seeAll && nonBusySessionsInSlot.length > 1;
								const hasSessions = nonBusySessionsInSlot.length > 0;
								const showNamesForSession =
									shouldShowNamesForSession(existingSession);

								const cellId = `${dayIndex}-${timeIndex}`;

								return (
									<div
										key={day}
										className="tooltip"
										data-tip={
											hasMultipleSessions
												? null
												: nonBusySessionsInSlot[0]?.note
										}
									>
										<DroppableSessionCell id={cellId}>
											<DraggableSessionCell
												id={cellId}
												hasSession={hasSessions}
												seeAll={seeAll}
											>
												<div className="flex items-center justify-center p-1">
													{hasMultipleSessions ? null : nonBusySessionsInSlot[0]
															?.note && showNamesForSession ? (
														<div className="absolute top-3 right-3 inline-grid *:[grid-area:1/1]">
															<div className="status status-error animate-ping" />
															<div className="status status-error" />
														</div>
													) : null}
													<button
														type="button"
														className={`flex w-full items-center justify-center rounded border-1 border-base-content/25 px-2 py-1 font-medium text-xs transition-colors ${
															hasSessions && !showNamesForSession
																? "bg-[#ff5c9b]"
																: ""
														} ${hasMultipleSessions ? "h-20" : "h-16"}`}
														onClick={() => handleCellClick(dayIndex, timeIndex)}
													>
														{hasSessions ? (
															<div className="w-full text-center">
																{hasMultipleSessions ? (
																	<div className="space-y-1">
																		{(seeAll && showNames) ||
																		(selectedClient && showNames) ? (
																			<div className="space-y-0.5 text-xs">
																				{nonBusySessionsInSlot.map(
																					(session) => (
																						<div
																							key={session.id}
																							className="truncate"
																						>
																							{selectedClient ? (
																								<>
																									<span className="font-bold">
																										{session.client.person.name}
																									</span>{" "}
																									- {session.worker.person.name}
																								</>
																							) : (
																								<>
																									{session.client.person.name} -{" "}
																									<span className="font-bold">
																										{session.worker.person.name}
																									</span>
																								</>
																							)}
																						</div>
																					),
																				)}
																			</div>
																		) : null}
																	</div>
																) : existingSession?.client.id ===
																	"0" ? null : (
																	<>
																		{seeAll && showNames ? (
																			<div className="font-semibold">
																				{existingSession?.client.person.name} -{" "}
																				<span className="font-bold">
																					{existingSession?.worker.person.name}
																				</span>
																			</div>
																		) : showNamesForSession ? (
																			selectedWorker ? (
																				<div className="font-semibold">
																					{existingSession?.client.person.name}{" "}
																					{
																						existingSession?.client.person
																							.fatherLastName
																					}{" "}
																					{
																						existingSession?.client.person
																							.motherLastName
																					}
																				</div>
																			) : selectedClient ? (
																				<div className="space-y-0.5">
																					<div className="font-bold text-sm">
																						{
																							existingSession?.client.person
																								.name
																						}{" "}
																						{
																							existingSession?.client.person
																								.fatherLastName
																						}{" "}
																						{
																							existingSession?.client.person
																								.motherLastName
																						}
																					</div>
																					<div className="font-medium text-xs opacity-80">
																						{
																							existingSession?.worker.person
																								.name
																						}{" "}
																						{
																							existingSession?.worker.person
																								.fatherLastName
																						}
																					</div>
																				</div>
																			) : (
																				<div className="font-semibold">
																					{existingSession?.worker.person.name}{" "}
																					{
																						existingSession?.worker.person
																							.fatherLastName
																					}{" "}
																					{
																						existingSession?.worker.person
																							.motherLastName
																					}
																				</div>
																			)
																		) : null}
																	</>
																)}
															</div>
														) : showNames ? (
															""
														) : (
															"Disponible"
														)}
													</button>
												</div>
											</DraggableSessionCell>
										</DroppableSessionCell>
									</div>
								);
							})}
						</React.Fragment>
					))}
				</div>
			</DndContext>

			<SessionModal
				isOpen={sessionModal.isOpen}
				onClose={handleModalClose}
				dayIndex={sessionModal.dayIndex}
				timeIndex={sessionModal.timeIndex}
				existingSession={getSessionForSlot(
					sessionModal.dayIndex,
					sessionModal.timeIndex,
				)}
			/>
		</>
	);
}
